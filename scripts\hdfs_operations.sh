#!/bin/bash

# HDFS操作脚本
# 用于上传文件到HDFS和下载结果文件

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查HDFS是否可用
check_hdfs() {
    log_info "检查HDFS连接..."
    if ! hdfs dfs -ls / > /dev/null 2>&1; then
        log_error "无法连接到HDFS，请检查Hadoop配置"
        exit 1
    fi
    log_info "HDFS连接正常"
}

# 创建HDFS目录
create_hdfs_dirs() {
    log_info "创建HDFS目录..."
    
    # 创建输入目录
    hdfs dfs -mkdir -p /user/input
    if [ $? -eq 0 ]; then
        log_info "输入目录创建成功: /user/input"
    else
        log_warn "输入目录可能已存在: /user/input"
    fi
    
    # 创建输出目录（如果存在则删除）
    hdfs dfs -rm -r /user/output > /dev/null 2>&1
    hdfs dfs -mkdir -p /user/output
    if [ $? -eq 0 ]; then
        log_info "输出目录创建成功: /user/output"
    else
        log_error "输出目录创建失败"
        exit 1
    fi
}

# 上传文件到HDFS
upload_files() {
    log_info "上传数据文件到HDFS..."
    
    # 检查本地文件是否存在
    local files=("data/file1.txt" "data/file2.txt" "data/file3.txt")
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "文件不存在: $file"
            exit 1
        fi
    done
    
    # 上传文件
    for file in "${files[@]}"; do
        log_info "上传文件: $file"
        hdfs dfs -put "$file" /user/input/
        if [ $? -eq 0 ]; then
            log_info "文件上传成功: $file -> /user/input/$(basename $file)"
        else
            log_error "文件上传失败: $file"
            exit 1
        fi
    done
    
    # 验证上传
    log_info "验证上传的文件:"
    hdfs dfs -ls /user/input/
}

# 下载结果文件
download_results() {
    log_info "下载结果文件..."
    
    # 创建本地结果目录
    mkdir -p results
    
    # 检查HDFS输出目录是否存在
    if ! hdfs dfs -test -d /user/output; then
        log_error "HDFS输出目录不存在: /user/output"
        exit 1
    fi
    
    # 下载结果文件
    log_info "从HDFS下载结果文件到本地results目录..."
    hdfs dfs -get /user/output/* results/
    if [ $? -eq 0 ]; then
        log_info "结果文件下载成功"
        log_info "本地结果文件:"
        ls -la results/
    else
        log_error "结果文件下载失败"
        exit 1
    fi
}

# 查看结果
view_results() {
    log_info "查看词频统计结果..."
    
    # 查找CSV文件
    csv_file=$(find results/ -name "*.csv" | head -1)
    if [ -n "$csv_file" ]; then
        log_info "词频统计结果 (前20行):"
        echo "----------------------------------------"
        head -20 "$csv_file"
        echo "----------------------------------------"
        log_info "完整结果文件: $csv_file"
    else
        log_warn "未找到CSV结果文件"
        log_info "results目录内容:"
        ls -la results/
    fi
}

# 清理HDFS目录
cleanup_hdfs() {
    log_info "清理HDFS目录..."
    hdfs dfs -rm -r /user/input > /dev/null 2>&1
    hdfs dfs -rm -r /user/output > /dev/null 2>&1
    log_info "HDFS目录清理完成"
}

# 主函数
main() {
    case "$1" in
        "upload")
            check_hdfs
            create_hdfs_dirs
            upload_files
            ;;
        "download")
            check_hdfs
            download_results
            view_results
            ;;
        "cleanup")
            check_hdfs
            cleanup_hdfs
            ;;
        "check")
            check_hdfs
            log_info "HDFS目录结构:"
            hdfs dfs -ls -R /user/ 2>/dev/null || log_warn "用户目录不存在"
            ;;
        *)
            echo "用法: $0 {upload|download|cleanup|check}"
            echo "  upload   - 上传数据文件到HDFS"
            echo "  download - 从HDFS下载结果文件"
            echo "  cleanup  - 清理HDFS目录"
            echo "  check    - 检查HDFS状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
