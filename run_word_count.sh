#!/bin/bash

# 分布式词频统计完整执行脚本
# 实现：创建文件 -> 上传HDFS -> Spark处理 -> 下载结果 -> 查看结果

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    分布式词频统计系统"
    echo "    Distributed Word Count System"
    echo "=================================================="
    echo -e "${NC}"
    echo "任务流程："
    echo "1. 构建Spark应用程序"
    echo "2. 上传数据文件到HDFS"
    echo "3. 提交Spark作业到YARN集群"
    echo "4. 下载处理结果"
    echo "5. 查看词频统计结果"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查Java
    if ! command -v java > /dev/null 2>&1; then
        missing_deps+=("java")
    fi
    
    # 检查Maven
    if ! command -v mvn > /dev/null 2>&1; then
        missing_deps+=("maven")
    fi
    
    # 检查Hadoop
    if ! command -v hdfs > /dev/null 2>&1; then
        missing_deps+=("hadoop")
    fi
    
    # 检查Spark
    if ! command -v spark-submit > /dev/null 2>&1; then
        missing_deps+=("spark")
    fi
    
    # 检查YARN
    if ! command -v yarn > /dev/null 2>&1; then
        missing_deps+=("yarn")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_error "请安装缺少的组件后重试"
        exit 1
    fi
    
    log_info "所有依赖检查通过"
}

# 构建项目
build_project() {
    log_step "构建Spark应用程序..."
    
    if [ ! -f "pom.xml" ]; then
        log_error "pom.xml文件不存在"
        exit 1
    fi
    
    log_info "执行Maven构建..."
    mvn clean package -DskipTests
    
    if [ $? -ne 0 ]; then
        log_error "Maven构建失败"
        exit 1
    fi
    
    if [ ! -f "target/spark-word-count-1.0.0.jar" ]; then
        log_error "JAR文件构建失败"
        exit 1
    fi
    
    log_success "Spark应用程序构建成功"
}

# 上传数据到HDFS
upload_data() {
    log_step "上传数据文件到HDFS..."
    
    chmod +x scripts/hdfs_operations.sh
    ./scripts/hdfs_operations.sh upload
    
    if [ $? -ne 0 ]; then
        log_error "数据上传失败"
        exit 1
    fi
    
    log_success "数据文件上传成功"
}

# 提交Spark作业
submit_job() {
    log_step "提交Spark作业到YARN集群..."
    
    chmod +x scripts/submit_spark_job.sh
    
    # 默认使用Scala版本，如果构建失败则使用Python版本
    if [ -f "target/spark-word-count-1.0.0.jar" ]; then
        log_info "使用Scala版本的Spark应用"
        ./scripts/submit_spark_job.sh scala
    else
        log_info "使用Python版本的Spark应用"
        ./scripts/submit_spark_job.sh python
    fi
    
    if [ $? -ne 0 ]; then
        log_error "Spark作业提交失败"
        exit 1
    fi
    
    log_success "Spark作业提交成功"
    
    # 等待作业完成
    log_info "等待作业完成..."
    sleep 30  # 给作业一些时间来完成
}

# 下载结果
download_results() {
    log_step "下载处理结果..."
    
    ./scripts/hdfs_operations.sh download
    
    if [ $? -ne 0 ]; then
        log_error "结果下载失败"
        exit 1
    fi
    
    log_success "结果文件下载成功"
}

# 显示结果摘要
show_summary() {
    log_step "任务执行摘要..."
    
    echo -e "${BLUE}=================================================="
    echo "              任务执行完成"
    echo "==================================================${NC}"
    
    echo ""
    echo "📁 输入文件:"
    ls -la data/
    
    echo ""
    echo "📊 处理结果:"
    ls -la results/
    
    echo ""
    echo "🔍 词频统计结果预览:"
    csv_file=$(find results/ -name "*.csv" | head -1)
    if [ -n "$csv_file" ]; then
        echo "文件: $csv_file"
        echo "----------------------------------------"
        head -10 "$csv_file" 2>/dev/null || echo "无法读取结果文件"
        echo "----------------------------------------"
    else
        echo "未找到CSV结果文件"
    fi
    
    echo ""
    echo "✅ 任务流程已完成！"
    echo ""
    echo "📋 后续操作建议:"
    echo "   - 查看完整结果: cat results/*.csv"
    echo "   - 监控YARN应用: yarn application -list"
    echo "   - 清理HDFS: ./scripts/hdfs_operations.sh cleanup"
}

# 清理函数
cleanup() {
    log_info "执行清理操作..."
    if [ -f "scripts/hdfs_operations.sh" ]; then
        chmod +x scripts/hdfs_operations.sh
        ./scripts/hdfs_operations.sh cleanup
    fi
}

# 错误处理
handle_error() {
    log_error "脚本执行过程中发生错误"
    log_info "正在执行清理操作..."
    cleanup
    exit 1
}

# 设置错误处理
trap handle_error ERR

# 显示帮助信息
show_help() {
    echo "分布式词频统计系统执行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --skip-build   跳过Maven构建步骤"
    echo "  --cleanup      仅执行清理操作"
    echo "  --check        仅检查系统依赖"
    echo ""
    echo "示例:"
    echo "  $0              # 执行完整流程"
    echo "  $0 --skip-build # 跳过构建步骤"
    echo "  $0 --cleanup    # 清理HDFS目录"
    echo "  $0 --check      # 检查系统依赖"
}

# 主函数
main() {
    local skip_build=false
    local cleanup_only=false
    local check_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --cleanup)
                cleanup_only=true
                shift
                ;;
            --check)
                check_only=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示横幅
    show_banner
    
    # 仅清理
    if [ "$cleanup_only" = true ]; then
        cleanup
        log_success "清理完成"
        exit 0
    fi
    
    # 仅检查依赖
    if [ "$check_only" = true ]; then
        check_dependencies
        log_success "依赖检查完成"
        exit 0
    fi
    
    # 执行完整流程
    check_dependencies
    
    if [ "$skip_build" = false ]; then
        build_project
    fi
    
    upload_data
    submit_job
    download_results
    show_summary
    
    log_success "所有任务执行完成！"
}

# 执行主函数
main "$@"
