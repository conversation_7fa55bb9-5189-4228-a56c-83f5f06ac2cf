# 分布式词频统计系统

这是一个完整的分布式词频统计系统，使用Apache Spark在YARN集群上处理存储在HDFS中的文本数据。

## 项目结构

```
分布式计算框架/
├── data/                          # 测试数据文件
│   ├── file1.txt                 # 关于Spark的文本
│   ├── file2.txt                 # 关于HDFS的文本
│   └── file3.txt                 # 关于YARN的文本
├── src/
│   └── main/
│       ├── scala/
│       │   └── WordCount.scala   # Scala版本的词频统计程序
│       └── python/
│           └── word_count.py     # Python版本的词频统计程序
├── scripts/
│   ├── hdfs_operations.sh        # HDFS操作脚本
│   └── submit_spark_job.sh       # Spark作业提交脚本
├── results/                      # 结果输出目录（运行后生成）
├── pom.xml                       # Maven构建配置
├── run_word_count.sh             # 主执行脚本
└── README.md                     # 项目说明文档
```

## 系统要求

### 必需组件
- **Java 8+**: Spark和Hadoop运行环境
- **Apache Hadoop 3.x**: 提供HDFS和YARN
- **Apache Spark 3.x**: 分布式计算引擎
- **Maven 3.x**: 项目构建工具

### 环境配置
确保以下环境变量已正确设置：
```bash
export JAVA_HOME=/path/to/java
export HADOOP_HOME=/path/to/hadoop
export SPARK_HOME=/path/to/spark
export PATH=$PATH:$HADOOP_HOME/bin:$SPARK_HOME/bin
```

## 快速开始

### 1. 一键执行（推荐）
```bash
# 给脚本执行权限
chmod +x run_word_count.sh

# 执行完整流程
./run_word_count.sh
```

### 2. 分步执行

#### 步骤1: 检查系统依赖
```bash
./run_word_count.sh --check
```

#### 步骤2: 构建Spark应用
```bash
mvn clean package
```

#### 步骤3: 上传数据到HDFS
```bash
chmod +x scripts/hdfs_operations.sh
./scripts/hdfs_operations.sh upload
```

#### 步骤4: 提交Spark作业
```bash
chmod +x scripts/submit_spark_job.sh
./scripts/submit_spark_job.sh scala  # 或者 python
```

#### 步骤5: 下载结果
```bash
./scripts/hdfs_operations.sh download
```

## 脚本说明

### 主执行脚本 (`run_word_count.sh`)
完整的自动化执行脚本，包含所有步骤。

**用法:**
```bash
./run_word_count.sh [选项]

选项:
  -h, --help     显示帮助信息
  --skip-build   跳过Maven构建步骤
  --cleanup      仅执行清理操作
  --check        仅检查系统依赖
```

### HDFS操作脚本 (`scripts/hdfs_operations.sh`)
管理HDFS文件操作。

**用法:**
```bash
./scripts/hdfs_operations.sh {upload|download|cleanup|check}

命令:
  upload   - 上传数据文件到HDFS
  download - 从HDFS下载结果文件
  cleanup  - 清理HDFS目录
  check    - 检查HDFS状态
```

### Spark提交脚本 (`scripts/submit_spark_job.sh`)
提交Spark作业到YARN集群。

**用法:**
```bash
./scripts/submit_spark_job.sh [选项] {scala|python|monitor}

命令:
  scala    - 提交Scala版本的Spark作业
  python   - 提交Python版本的Spark作业
  monitor  - 监控YARN应用状态

选项:
  --driver-memory MEM     设置Driver内存 (默认: 1g)
  --executor-memory MEM   设置Executor内存 (默认: 1g)
  --executor-cores NUM    设置Executor核心数 (默认: 2)
  --num-executors NUM     设置Executor数量 (默认: 2)
  --queue QUEUE           设置YARN队列 (默认: default)
```

## 程序说明

### Scala版本 (`src/main/scala/WordCount.scala`)
- 使用Spark SQL DataFrame API
- 支持大规模数据处理
- 自动优化查询执行计划
- 输出CSV格式结果

### Python版本 (`src/main/python/word_count.py`)
- 使用PySpark DataFrame API
- 与Scala版本功能相同
- 更易于理解和修改

## 输出结果

程序会在以下位置生成结果：
- **HDFS**: `/user/output/` 目录
- **本地**: `results/` 目录

结果文件格式为CSV，包含两列：
- `word`: 单词
- `count`: 出现次数

## 监控和调试

### 查看YARN应用状态
```bash
yarn application -list
yarn application -status <APPLICATION_ID>
```

### 查看应用日志
```bash
yarn logs -applicationId <APPLICATION_ID>
```

### 查看HDFS文件
```bash
hdfs dfs -ls /user/
hdfs dfs -cat /user/output/*.csv
```

## 故障排除

### 常见问题

1. **无法连接到HDFS**
   - 检查Hadoop服务是否启动
   - 验证HADOOP_HOME环境变量
   - 确认HDFS配置正确

2. **Spark作业提交失败**
   - 检查YARN服务状态
   - 验证SPARK_HOME环境变量
   - 确认资源配置合理

3. **Maven构建失败**
   - 检查网络连接
   - 清理Maven缓存: `mvn clean`
   - 检查Java版本兼容性

### 清理环境
```bash
# 清理HDFS目录
./scripts/hdfs_operations.sh cleanup

# 清理本地构建文件
mvn clean

# 清理结果目录
rm -rf results/
```

## 扩展功能

### 自定义配置
可以通过修改脚本中的配置参数来调整：
- Spark资源配置
- HDFS路径
- 输出格式
- 过滤条件

### 添加新的数据源
1. 将新文件放入`data/`目录
2. 修改`hdfs_operations.sh`中的文件列表
3. 重新运行流程

## 许可证

本项目仅用于学习和教育目的。
