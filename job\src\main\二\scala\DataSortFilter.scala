import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object DataSortFilter {
  def main(args: Array[String]): Unit = {
    if (args.length < 2) {
      System.err.println("Usage: DataSortFilter <input_path> <output_path>")
      System.exit(1)
    }

    val inputPath = args(0)
    val outputPath = args(1)

    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("DataSortFilter")
      .getOrCreate()

    import spark.implicits._

    try {
      // 读取输入文件
      val schema = StructType(Array(
        StructField("student_id", StringType, true),
        StructField("name", StringType, true),
        StructField("subject", StringType, true),
        StructField("score", IntegerType, true),
        StructField("grade", StringType, true)
      ))

      // 如果输入是文本文件，我们需要解析数据
      val rawData = spark.read.textFile(inputPath)
      
      // 解析文本数据为结构化数据
      val studentData = rawData
        .filter(line => line.trim.nonEmpty && !line.startsWith("#")) // 过滤空行和注释行
        .map(line => {
          val parts = line.split(",")
          if (parts.length >= 5) {
            (parts(0).trim, parts(1).trim, parts(2).trim, 
             try { parts(3).trim.toInt } catch { case _: NumberFormatException => 0 },
             parts(4).trim)
          } else {
            ("", "", "", 0, "")
          }
        })
        .filter(_._1.nonEmpty) // 过滤无效数据
        .toDF("student_id", "name", "subject", "score", "grade")

      // 数据处理：排序和过滤
      val processedData = studentData
        .filter($"score" >= 60) // 过滤及格分数
        .filter($"subject".isNotNull && $"subject" =!= "") // 过滤有效科目
        .orderBy(desc("score"), asc("name")) // 按分数降序，姓名升序排列
        .withColumn("performance_level", 
          when($"score" >= 90, "优秀")
          .when($"score" >= 80, "良好")
          .when($"score" >= 70, "中等")
          .when($"score" >= 60, "及格")
          .otherwise("不及格")
        )

      // 统计信息
      val totalCount = studentData.count()
      val passCount = processedData.count()
      val passRate = if (totalCount > 0) (passCount.toDouble / totalCount * 100) else 0.0

      println(s"数据处理完成！")
      println(s"总记录数: $totalCount")
      println(s"及格记录数: $passCount")
      println(s"及格率: ${f"$passRate%.2f"}%")

      // 显示处理后的数据样本
      println("处理后的数据样本（前20条）:")
      processedData.show(20)

      // 按科目统计
      println("各科目统计:")
      processedData
        .groupBy("subject")
        .agg(
          count("*").alias("student_count"),
          avg("score").alias("avg_score"),
          max("score").alias("max_score"),
          min("score").alias("min_score")
        )
        .orderBy(desc("avg_score"))
        .show()

      // 按等级统计
      println("成绩等级分布:")
      processedData
        .groupBy("performance_level")
        .count()
        .orderBy(desc("count"))
        .show()

      // 保存结果到HDFS
      processedData
        .coalesce(1) // 合并为单个文件
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(outputPath)

      println(s"排序过滤结果已保存到: $outputPath")

    } catch {
      case e: Exception =>
        System.err.println(s"执行过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    } finally {
      spark.stop()
    }
  }
}
