YARN (Yet Another Resource Negotiator) is a cluster management technology.
YARN is one of the key features in the second-generation Hadoop 2 version of the Apache Hadoop distributed computing framework.
Originally described by Apache as a redesigned resource manager, YARN is characterized as a large-scale distributed operating system for big data applications.
YARN allows multiple data processing engines such as interactive SQL, real-time streaming, data science and batch processing to handle data stored in a single platform.
This unlocks an entirely new approach to analytics and enables businesses to ask new questions of their data.
YARN provides resource management and a central platform to deliver consistent operations, security, and data governance tools across Hadoop clusters.
YARN enables multiple applications to run in Hadoop and share a common resource pool in an efficient, ordered manner.
YARN separates the resource management and processing components.
The idea is to have a global ResourceManager and per-application ApplicationMaster.
YARN supports the notion of resource reservation via the ReservationSystem, a component that allows users to specify a profile of resources over-time and temporal constraints.
