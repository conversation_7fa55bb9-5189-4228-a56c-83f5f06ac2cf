Hadoop Distributed File System (HDFS) is a distributed file system designed to run on commodity hardware.
HDFS is highly fault-tolerant and is designed to be deployed on low-cost hardware.
HDFS provides high throughput access to application data and is suitable for applications that have large data sets.
HDFS relaxes a few POSIX requirements to enable streaming access to file system data.
HDFS was originally built as infrastructure for the Apache Nutch web search engine project.
HDFS is now an Apache Hadoop subproject.
The Hadoop project includes these modules: Hadoop Common, Hadoop Distributed File System, Hadoop YARN, and Hadoop MapReduce.
HDFS stores large files across multiple machines and achieves reliability by replicating the data across multiple hosts.
HDFS is designed to reliably store very large files across machines in a large cluster.
It stores each file as a sequence of blocks and all blocks in a file except the last block are the same size.
