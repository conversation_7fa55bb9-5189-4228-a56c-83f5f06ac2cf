<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.example</groupId>
  <artifactId>spark-word-count</artifactId>
  <name>Spark Word Count</name>
  <version>1.0.0</version>
  <description>Spark应用程序用于词频统计</description>
  <build>
    <sourceDirectory>src/main/scala</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <version>4.5.6</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <scalaVersion>${scala.version}</scalaVersion>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>3.5.1</version>
        <configuration>
          <options>
            <option>--add-opens=java.base/java.lang=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.lang.invoke=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.lang.reflect=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.io=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.net=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.nio=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.util=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.util.concurrent=ALL-UNNAMED</option>
            <option>--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED</option>
            <option>--add-opens=java.base/sun.nio.ch=ALL-UNNAMED</option>
            <option>--add-opens=java.base/sun.nio.cs=ALL-UNNAMED</option>
            <option>--add-opens=java.base/sun.security.action=ALL-UNNAMED</option>
            <option>--add-opens=java.base/sun.util.calendar=ALL-UNNAMED</option>
            <option>--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED</option>
            <option>-Djdk.reflect.useDirectMethodHandle=false</option>
          </options>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>WordCount</mainClass>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <properties>
    <scala.binary.version>2.12</scala.binary.version>
    <maven.compiler.target>8</maven.compiler.target>
    <spark.version>3.3.0</spark.version>
    <maven.compiler.source>8</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <scala.version>2.12.15</scala.version>
  </properties>
</project>
