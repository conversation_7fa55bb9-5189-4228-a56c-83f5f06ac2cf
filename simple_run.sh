#!/bin/bash

# 简化版分布式词频统计执行脚本
# 适用于虚拟机环境的手动操作

echo "=== 分布式词频统计任务 ==="
echo ""

# 步骤1: 上传文件到HDFS
echo "步骤1: 上传文件到HDFS"
echo "执行以下命令："
echo "hdfs dfs -mkdir -p /user/input"
echo "hdfs dfs -put file1.txt /user/input/"
echo "hdfs dfs -put file2.txt /user/input/"
echo "hdfs dfs -put file3.txt /user/input/"
echo "hdfs dfs -ls /user/input/"
echo ""

# 步骤2: 编译Spark程序
echo "步骤2: 编译Spark程序"
echo "执行以下命令："
echo "mvn clean package -DskipTests"
echo ""

# 步骤3: 提交Spark作业
echo "步骤3: 提交Spark作业"
echo "执行以下命令："
echo "hdfs dfs -rm -r /user/output"
echo "spark-submit \\"
echo "  --class WordCount \\"
echo "  --master yarn \\"
echo "  --deploy-mode cluster \\"
echo "  --name \"WordCount-Job\" \\"
echo "  --driver-memory 1g \\"
echo "  --executor-memory 1g \\"
echo "  --executor-cores 2 \\"
echo "  --num-executors 2 \\"
echo "  target/spark-word-count-1.0.0.jar \\"
echo "  /user/input \\"
echo "  /user/output"
echo ""

# 步骤4: 下载结果
echo "步骤4: 下载结果文件"
echo "执行以下命令："
echo "hdfs dfs -ls /user/output/"
echo "mkdir -p results"
echo "hdfs dfs -get /user/output/* results/"
echo "cat results/*.csv | head -20"
echo ""

echo "=== 任务完成 ==="
