Apache Spark is a unified analytics engine for large-scale data processing.
Spark provides high-level APIs in Java, Scala, Python and R.
It also supports a rich set of higher-level tools including Spark SQL for SQL and structured data processing.
MLlib for machine learning, GraphX for graph processing, and Structured Streaming for incremental computation and stream processing.
Spark runs on Hadoop, Apache Mesos, Kubernetes, standalone, or in the cloud.
It can access diverse data sources including HDFS, Alluxio, Apache Cassandra, Apache HBase, Apache Hive, and hundreds of other data sources.
Spark is designed to be fast and general purpose.
The key insight behind Spark is that many iterative algorithms and interactive data mining tools can benefit from caching datasets across iterations.
Spark provides primitives for in-memory cluster computing that lets user programs load data into a cluster's memory and query it repeatedly.
This makes Spark well-suited for machine learning algorithms and interactive analytics.
