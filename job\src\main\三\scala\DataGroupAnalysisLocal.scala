import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object DataGroupAnalysisLocal {
  def main(args: Array[String]): Unit = {
    // 本地运行时使用默认路径
    val inputPath = if (args.length > 0) args(0) else "job/data/sales_data.txt"
    val outputPath = if (args.length > 1) args(1) else "job/output/group_analysis_result"

    // 创建SparkSession - 本地运行配置
    val spark = SparkSession.builder()
      .appName("DataGroupAnalysis-Local")
      .master("local[*]") // 使用本地模式，利用所有CPU核心
      .config("spark.sql.warehouse.dir", "job/spark-warehouse") // 本地warehouse目录
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .getOrCreate()

    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")

    import spark.implicits._

    try {
      println(s"读取输入文件: $inputPath")
      
      // 读取输入文件
      val rawData = spark.read.textFile(inputPath)
      
      // 解析销售数据：日期,产品类别,产品名称,销售数量,单价,销售员
      val salesData = rawData
        .filter(line => line.trim.nonEmpty && !line.startsWith("#")) // 过滤空行和注释行
        .map(line => {
          val parts = line.split(",")
          if (parts.length >= 6) {
            (
              parts(0).trim, // 日期
              parts(1).trim, // 产品类别
              parts(2).trim, // 产品名称
              try { parts(3).trim.toInt } catch { case _: NumberFormatException => 0 }, // 销售数量
              try { parts(4).trim.toDouble } catch { case _: NumberFormatException => 0.0 }, // 单价
              parts(5).trim  // 销售员
            )
          } else {
            ("", "", "", 0, 0.0, "")
          }
        })
        .filter(_._1.nonEmpty) // 过滤无效数据
        .toDF("sale_date", "category", "product_name", "quantity", "unit_price", "salesperson")
        .withColumn("total_amount", $"quantity" * $"unit_price") // 计算总金额
        .withColumn("month", substring($"sale_date", 1, 7)) // 提取年月

      println("原始销售数据样本:")
      salesData.show(10)

      // 1. 按产品类别分组统计
      println("\n=== 按产品类别分组统计 ===")
      val categoryStats = salesData
        .groupBy("category")
        .agg(
          sum("quantity").alias("total_quantity"),
          sum("total_amount").alias("total_revenue"),
          avg("unit_price").alias("avg_unit_price"),
          count("*").alias("transaction_count"),
          countDistinct("product_name").alias("product_variety")
        )
        .withColumn("avg_revenue_per_transaction", 
          round($"total_revenue" / $"transaction_count", 2))
        .orderBy(desc("total_revenue"))

      categoryStats.show()

      // 2. 按销售员分组统计
      println("\n=== 按销售员分组统计 ===")
      val salespersonStats = salesData
        .groupBy("salesperson")
        .agg(
          sum("total_amount").alias("total_sales"),
          sum("quantity").alias("total_items_sold"),
          count("*").alias("transaction_count"),
          countDistinct("category").alias("categories_handled"),
          avg("total_amount").alias("avg_transaction_value")
        )
        .withColumn("performance_rank", 
          row_number().over(
            org.apache.spark.sql.expressions.Window.orderBy(desc("total_sales"))
          ))
        .orderBy("performance_rank")

      salespersonStats.show()

      // 3. 按月份分组统计
      println("\n=== 按月份分组统计 ===")
      val monthlyStats = salesData
        .groupBy("month")
        .agg(
          sum("total_amount").alias("monthly_revenue"),
          sum("quantity").alias("monthly_quantity"),
          count("*").alias("monthly_transactions"),
          countDistinct("salesperson").alias("active_salespeople")
        )
        .withColumn("avg_daily_revenue", 
          round($"monthly_revenue" / 30, 2)) // 假设每月30天
        .orderBy("month")

      monthlyStats.show()

      // 4. 产品销售排行榜
      println("\n=== 产品销售排行榜（Top 10）===")
      val productRanking = salesData
        .groupBy("product_name", "category")
        .agg(
          sum("quantity").alias("total_sold"),
          sum("total_amount").alias("total_revenue"),
          count("*").alias("sale_frequency")
        )
        .orderBy(desc("total_revenue"))
        .limit(10)

      productRanking.show()

      // 5. 综合分析报告
      println("\n=== 综合分析报告 ===")
      val totalRevenue = salesData.agg(sum("total_amount")).collect()(0).getDouble(0)
      val totalTransactions = salesData.count()
      val avgTransactionValue = totalRevenue / totalTransactions
      val topCategory = categoryStats.first().getString(0)
      val topSalesperson = salespersonStats.first().getString(0)

      println(f"总销售额: ¥${totalRevenue}%.2f")
      println(f"总交易数: $totalTransactions")
      println(f"平均交易金额: ¥${avgTransactionValue}%.2f")
      println(s"最佳销售类别: $topCategory")
      println(s"最佳销售员: $topSalesperson")

      // 6. 创建汇总数据集
      val summaryData = salesData
        .groupBy("category", "month")
        .agg(
          sum("total_amount").alias("revenue"),
          sum("quantity").alias("quantity_sold"),
          count("*").alias("transactions"),
          countDistinct("salesperson").alias("salespeople_count")
        )
        .withColumn("revenue_per_transaction", 
          round($"revenue" / $"transactions", 2))
        .orderBy("category", "month")

      println("\n=== 类别月度汇总 ===")
      summaryData.show()

      // 保存结果到本地文件
      println(s"\n保存结果到: $outputPath")
      
      // 创建输出目录
      import java.io.File
      new File(outputPath).mkdirs()

      // 保存各种统计结果
      categoryStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/category_stats")

      salespersonStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/salesperson_stats")

      monthlyStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/monthly_stats")

      productRanking
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/product_ranking")

      summaryData
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/summary_data")

      println(s"\n✅ 数据分组分析任务完成！")
      println(s"结果已保存到: $outputPath")

    } catch {
      case e: Exception =>
        System.err.println(s"执行过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    } finally {
      spark.stop()
    }
  }
}
