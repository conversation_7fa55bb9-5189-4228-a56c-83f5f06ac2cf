#!/bin/bash

# 分布式计算框架作业执行脚本
# 用于在虚拟机上执行三个不同的Spark作业

echo "=== 分布式计算框架作业执行脚本 ==="
echo ""

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <job_number> [input_path] [output_path]"
    echo "job_number: 1(词频统计), 2(数据排序过滤), 3(数据分组分析)"
    echo "示例:"
    echo "  $0 1 /user/input /user/output1"
    echo "  $0 2 /user/data/student_scores.txt /user/output2"
    echo "  $0 3 /user/data/sales_data.txt /user/output3"
    exit 1
fi

JOB_NUMBER=$1
INPUT_PATH=${2:-"/user/input"}
OUTPUT_PATH=${3:-"/user/output"}

# JAR文件路径
JAR_FILE="spark-word-count-1.0.0-shaded.jar"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件 $JAR_FILE 不存在"
    echo "请先编译项目: mvn clean package"
    exit 1
fi

# 根据作业号选择主类
case $JOB_NUMBER in
    1)
        MAIN_CLASS="WordCount"
        JOB_NAME="WordCount-Job"
        echo "执行第1题: 词频统计"
        ;;
    2)
        MAIN_CLASS="DataSortFilter"
        JOB_NAME="DataSortFilter-Job"
        echo "执行第2题: 数据排序和过滤"
        ;;
    3)
        MAIN_CLASS="DataGroupAnalysis"
        JOB_NAME="DataGroupAnalysis-Job"
        echo "执行第3题: 数据分组分析"
        ;;
    *)
        echo "错误: 无效的作业号 $JOB_NUMBER"
        echo "有效的作业号: 1, 2, 3"
        exit 1
        ;;
esac

echo "主类: $MAIN_CLASS"
echo "输入路径: $INPUT_PATH"
echo "输出路径: $OUTPUT_PATH"
echo ""

# 清理输出目录
echo "清理输出目录..."
hdfs dfs -rm -r $OUTPUT_PATH 2>/dev/null

# 提交Spark作业
echo "提交Spark作业..."
spark-submit \
  --class $MAIN_CLASS \
  --master yarn \
  --deploy-mode cluster \
  --name $JOB_NAME \
  --driver-memory 1g \
  --executor-memory 1g \
  --executor-cores 2 \
  --num-executors 2 \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.adaptive.coalescePartitions.enabled=true \
  $JAR_FILE \
  $INPUT_PATH \
  $OUTPUT_PATH

# 检查作业执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 作业执行成功！"
    echo ""
    echo "查看输出目录:"
    hdfs dfs -ls $OUTPUT_PATH/
    echo ""
    echo "查看结果文件:"
    hdfs dfs -ls $OUTPUT_PATH/ | grep -E "\\.csv$|\\.txt$" | head -5
    echo ""
    echo "预览结果内容:"
    hdfs dfs -cat $OUTPUT_PATH/*.csv 2>/dev/null | head -10 || \
    hdfs dfs -cat $OUTPUT_PATH/part-* 2>/dev/null | head -10
else
    echo ""
    echo "❌ 作业执行失败！"
    echo "请检查日志获取详细错误信息:"
    echo "yarn application -list -appStates FAILED"
fi

echo ""
echo "=== 脚本执行完成 ==="
