import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

object WordCount {
  def main(args: Array[String]): Unit = {
    if (args.length < 2) {
      System.err.println("Usage: WordCount <input_path> <output_path>")
      System.exit(1)
    }

    val inputPath = args(0)
    val outputPath = args(1)

    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("WordCount")
      .getOrCreate()

    import spark.implicits._

    try {
      // 读取输入文件
      val textFile = spark.read.textFile(inputPath)

      // 进行词频统计
      val wordCounts = textFile
        .flatMap(line => line.split("\\s+"))  // 按空格分割单词
        .filter(word => word.nonEmpty)       // 过滤空字符串
        .map(word => word.toLowerCase.replaceAll("[^a-zA-Z0-9]", ""))  // 转小写并移除标点
        .filter(word => word.nonEmpty)       // 再次过滤空字符串
        .groupBy("value")                    // 按单词分组
        .count()                             // 计数
        .orderBy(desc("count"))              // 按计数降序排列

      // 将结果写入HDFS
      wordCounts.coalesce(1)  // 合并为单个文件
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(outputPath)

      println(s"词频统计完成！结果已保存到: $outputPath")
      
      // 显示前20个最频繁的单词
      println("前20个最频繁的单词:")
      wordCounts.show(20)

    } catch {
      case e: Exception =>
        System.err.println(s"执行过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    } finally {
      spark.stop()
    }
  }
}
