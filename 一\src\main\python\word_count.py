#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import re
from pyspark.sql import SparkSession
from pyspark.sql.functions import *

def main():
    if len(sys.argv) < 3:
        print("Usage: word_count.py <input_path> <output_path>", file=sys.stderr)
        sys.exit(1)
    
    input_path = sys.argv[1]
    output_path = sys.argv[2]
    
    # 创建SparkSession
    spark = SparkSession.builder \
        .appName("WordCount") \
        .getOrCreate()
    
    try:
        # 读取输入文件
        text_file = spark.read.text(input_path)
        
        # 进行词频统计
        word_counts = text_file.select(
            explode(split(text_file.value, "\\s+")).alias("word")
        ).filter(
            col("word") != ""
        ).select(
            lower(regexp_replace(col("word"), "[^a-zA-Z0-9]", "")).alias("word")
        ).filter(
            col("word") != ""
        ).groupBy("word").count().orderBy(desc("count"))
        
        # 将结果写入HDFS
        word_counts.coalesce(1) \
            .write \
            .mode("overwrite") \
            .option("header", "true") \
            .csv(output_path)
        
        print(f"词频统计完成！结果已保存到: {output_path}")
        
        # 显示前20个最频繁的单词
        print("前20个最频繁的单词:")
        word_counts.show(20)
        
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
