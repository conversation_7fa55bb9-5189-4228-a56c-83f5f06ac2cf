@echo off
echo ===== 准备上传文件到虚拟机 =====
echo.

REM 创建上传目录
if not exist "upload" mkdir upload

REM 复制JAR文件（优先使用shaded版本）
if exist "target\spark-word-count-1.0.0-shaded.jar" (
    copy "target\spark-word-count-1.0.0-shaded.jar" "upload\"
    echo ✓ Shaded JAR文件已复制到upload目录
) else if exist "target\spark-word-count-1.0.0.jar" (
    copy "target\spark-word-count-1.0.0.jar" "upload\"
    echo ✓ 标准JAR文件已复制到upload目录
) else (
    echo ✗ JAR文件不存在，请先运行 mvn clean package
    pause
    exit /b 1
)

REM 复制数据文件
copy "file1.txt" "upload\"
copy "file2.txt" "upload\"
copy "file3.txt" "upload\"
echo ✓ 数据文件已复制到upload目录

REM 创建虚拟机执行脚本
echo #!/bin/bash > upload\run_on_vm.sh
echo # 虚拟机执行脚本 >> upload\run_on_vm.sh
echo echo "=== 开始执行分布式词频统计任务 ===" >> upload\run_on_vm.sh
echo. >> upload\run_on_vm.sh
echo # 步骤1: 上传数据文件到HDFS >> upload\run_on_vm.sh
echo echo "步骤1: 上传数据文件到HDFS" >> upload\run_on_vm.sh
echo hdfs dfs -mkdir -p /user/input >> upload\run_on_vm.sh
echo hdfs dfs -put file1.txt /user/input/ >> upload\run_on_vm.sh
echo hdfs dfs -put file2.txt /user/input/ >> upload\run_on_vm.sh
echo hdfs dfs -put file3.txt /user/input/ >> upload\run_on_vm.sh
echo hdfs dfs -ls /user/input/ >> upload\run_on_vm.sh
echo echo "✓ 数据文件上传完成" >> upload\run_on_vm.sh
echo. >> upload\run_on_vm.sh
echo # 步骤2: 清理并创建输出目录 >> upload\run_on_vm.sh
echo echo "步骤2: 准备输出目录" >> upload\run_on_vm.sh
echo hdfs dfs -rm -r /user/output 2^>/dev/null >> upload\run_on_vm.sh
echo hdfs dfs -mkdir -p /user/output >> upload\run_on_vm.sh
echo echo "✓ 输出目录准备完成" >> upload\run_on_vm.sh
echo. >> upload\run_on_vm.sh
echo # 步骤3: 提交Spark作业 >> upload\run_on_vm.sh
echo echo "步骤3: 提交Spark作业到YARN集群" >> upload\run_on_vm.sh
echo spark-submit \\ >> upload\run_on_vm.sh
echo   --class WordCount \\ >> upload\run_on_vm.sh
echo   --master yarn \\ >> upload\run_on_vm.sh
echo   --deploy-mode cluster \\ >> upload\run_on_vm.sh
echo   --name "WordCount-Job" \\ >> upload\run_on_vm.sh
echo   --driver-memory 1g \\ >> upload\run_on_vm.sh
echo   --executor-memory 1g \\ >> upload\run_on_vm.sh
echo   --executor-cores 2 \\ >> upload\run_on_vm.sh
echo   --num-executors 2 \\ >> upload\run_on_vm.sh
echo   spark-word-count-1.0.0-shaded.jar \\ >> upload\run_on_vm.sh
echo   /user/input \\ >> upload\run_on_vm.sh
echo   /user/output >> upload\run_on_vm.sh
echo echo "✓ Spark作业提交完成" >> upload\run_on_vm.sh
echo. >> upload\run_on_vm.sh
echo # 步骤4: 等待作业完成并下载结果 >> upload\run_on_vm.sh
echo echo "步骤4: 等待作业完成..." >> upload\run_on_vm.sh
echo sleep 60 >> upload\run_on_vm.sh
echo echo "检查输出目录:" >> upload\run_on_vm.sh
echo hdfs dfs -ls /user/output/ >> upload\run_on_vm.sh
echo echo "下载结果文件:" >> upload\run_on_vm.sh
echo mkdir -p results >> upload\run_on_vm.sh
echo hdfs dfs -get /user/output/* results/ >> upload\run_on_vm.sh
echo echo "词频统计结果预览:" >> upload\run_on_vm.sh
echo cat results/*.csv ^| head -20 >> upload\run_on_vm.sh
echo echo "=== 任务执行完成 ===" >> upload\run_on_vm.sh

echo ✓ 虚拟机执行脚本已创建

echo.
echo ===== 准备完成 =====
echo 请将upload目录中的所有文件上传到虚拟机，然后执行：
echo   chmod +x run_on_vm.sh
echo   ./run_on_vm.sh
echo.
echo upload目录包含：
dir upload
pause
