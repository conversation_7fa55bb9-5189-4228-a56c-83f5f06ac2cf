import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object DataSortFilterLocal {
  def main(args: Array[String]): Unit = {
    // 本地运行时使用默认路径
    val inputPath = if (args.length > 0) args(0) else "job/data/student_scores.txt"
    val outputPath = if (args.length > 1) args(1) else "job/output/sort_filter_result"

    // 创建SparkSession - 本地运行配置
    val spark = SparkSession.builder()
      .appName("DataSortFilter-Local")
      .master("local[*]") // 使用本地模式，利用所有CPU核心
      .config("spark.sql.warehouse.dir", "spark-warehouse") // 本地warehouse目录
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .getOrCreate()

    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")

    import spark.implicits._

    try {
      println(s"读取输入文件: $inputPath")
      
      // 读取输入文件
      val rawData = spark.read.textFile(inputPath)
      
      // 解析文本数据为结构化数据
      val studentData = rawData
        .filter(line => line.trim.nonEmpty && !line.startsWith("#")) // 过滤空行和注释行
        .map(line => {
          val parts = line.split(",")
          if (parts.length >= 5) {
            (parts(0).trim, parts(1).trim, parts(2).trim, 
             try { parts(3).trim.toInt } catch { case _: NumberFormatException => 0 },
             parts(4).trim)
          } else {
            ("", "", "", 0, "")
          }
        })
        .filter(_._1.nonEmpty) // 过滤无效数据
        .toDF("student_id", "name", "subject", "score", "grade")

      println("原始数据样本:")
      studentData.show(10)

      // 数据处理：排序和过滤
      val processedData = studentData
        .filter($"score" >= 60) // 过滤及格分数
        .filter($"subject".isNotNull && $"subject" =!= "") // 过滤有效科目
        .orderBy(desc("score"), asc("name")) // 按分数降序，姓名升序排列
        .withColumn("performance_level", 
          when($"score" >= 90, "优秀")
          .when($"score" >= 80, "良好")
          .when($"score" >= 70, "中等")
          .when($"score" >= 60, "及格")
          .otherwise("不及格")
        )

      // 统计信息
      val totalCount = studentData.count()
      val passCount = processedData.count()
      val passRate = if (totalCount > 0) (passCount.toDouble / totalCount * 100) else 0.0

      println(s"\n=== 数据处理结果 ===")
      println(s"总记录数: $totalCount")
      println(s"及格记录数: $passCount")
      println(s"及格率: ${f"$passRate%.2f"}%")

      // 显示处理后的数据样本
      println("\n处理后的数据（前20条）:")
      processedData.show(20)

      // 按科目统计
      println("\n=== 各科目统计 ===")
      val subjectStats = processedData
        .groupBy("subject")
        .agg(
          count("*").alias("student_count"),
          avg("score").alias("avg_score"),
          max("score").alias("max_score"),
          min("score").alias("min_score")
        )
        .orderBy(desc("avg_score"))
      
      subjectStats.show()

      // 按等级统计
      println("\n=== 成绩等级分布 ===")
      val levelStats = processedData
        .groupBy("performance_level")
        .count()
        .orderBy(desc("count"))
      
      levelStats.show()

      // 按年级统计
      println("\n=== 各年级统计 ===")
      val gradeStats = processedData
        .groupBy("grade")
        .agg(
          count("*").alias("student_count"),
          avg("score").alias("avg_score"),
          countDistinct("subject").alias("subject_count")
        )
        .orderBy("grade")
      
      gradeStats.show()

      // 保存结果到本地文件
      println(s"\n保存结果到: $outputPath")
      
      // 创建输出目录
      import java.io.File
      new File(outputPath).mkdirs()

      // 保存主要结果
      processedData
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/processed_data")

      // 保存统计结果
      subjectStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/subject_stats")

      levelStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/level_stats")

      gradeStats
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"$outputPath/grade_stats")

      println(s"\n✅ 数据排序过滤任务完成！")
      println(s"结果已保存到: $outputPath")

    } catch {
      case e: Exception =>
        System.err.println(s"执行过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    } finally {
      spark.stop()
    }
  }
}
