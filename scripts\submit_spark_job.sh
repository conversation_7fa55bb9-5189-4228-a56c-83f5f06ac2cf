#!/bin/bash

# Spark作业提交脚本
# 用于将Spark应用提交到YARN集群运行

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置参数
JAR_FILE="target/spark-word-count-1.0.0.jar"
MAIN_CLASS="WordCount"
INPUT_PATH="/user/input"
OUTPUT_PATH="/user/output"
APP_NAME="WordCount-$(date +%Y%m%d-%H%M%S)"

# Spark配置参数
DRIVER_MEMORY="1g"
EXECUTOR_MEMORY="1g"
EXECUTOR_CORES="2"
NUM_EXECUTORS="2"
QUEUE="default"

# 检查JAR文件是否存在
check_jar_file() {
    log_info "检查JAR文件..."
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件不存在: $JAR_FILE"
        log_info "请先运行 'mvn clean package' 构建项目"
        exit 1
    fi
    log_info "JAR文件存在: $JAR_FILE"
}

# 检查YARN是否可用
check_yarn() {
    log_info "检查YARN连接..."
    if ! yarn application -list > /dev/null 2>&1; then
        log_error "无法连接到YARN，请检查Hadoop配置"
        exit 1
    fi
    log_info "YARN连接正常"
}

# 检查Spark是否可用
check_spark() {
    log_info "检查Spark环境..."
    if ! command -v spark-submit > /dev/null 2>&1; then
        log_error "spark-submit命令不可用，请检查Spark安装"
        exit 1
    fi
    log_info "Spark环境正常"
}

# 提交Scala版本的Spark作业
submit_scala_job() {
    log_info "提交Scala版本的Spark作业到YARN集群..."
    log_debug "应用名称: $APP_NAME"
    log_debug "输入路径: $INPUT_PATH"
    log_debug "输出路径: $OUTPUT_PATH"
    
    spark-submit \
        --class $MAIN_CLASS \
        --master yarn \
        --deploy-mode cluster \
        --name "$APP_NAME" \
        --driver-memory $DRIVER_MEMORY \
        --executor-memory $EXECUTOR_MEMORY \
        --executor-cores $EXECUTOR_CORES \
        --num-executors $NUM_EXECUTORS \
        --queue $QUEUE \
        --conf spark.sql.adaptive.enabled=true \
        --conf spark.sql.adaptive.coalescePartitions.enabled=true \
        --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
        $JAR_FILE \
        $INPUT_PATH $OUTPUT_PATH
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_info "Spark作业提交成功！"
        return 0
    else
        log_error "Spark作业提交失败，退出码: $exit_code"
        return $exit_code
    fi
}

# 提交Python版本的Spark作业
submit_python_job() {
    log_info "提交Python版本的Spark作业到YARN集群..."
    log_debug "应用名称: $APP_NAME-Python"
    log_debug "输入路径: $INPUT_PATH"
    log_debug "输出路径: $OUTPUT_PATH"
    
    spark-submit \
        --master yarn \
        --deploy-mode cluster \
        --name "$APP_NAME-Python" \
        --driver-memory $DRIVER_MEMORY \
        --executor-memory $EXECUTOR_MEMORY \
        --executor-cores $EXECUTOR_CORES \
        --num-executors $NUM_EXECUTORS \
        --queue $QUEUE \
        --conf spark.sql.adaptive.enabled=true \
        --conf spark.sql.adaptive.coalescePartitions.enabled=true \
        --conf spark.pyspark.python=python3 \
        src/main/python/word_count.py \
        $INPUT_PATH $OUTPUT_PATH
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_info "Python Spark作业提交成功！"
        return 0
    else
        log_error "Python Spark作业提交失败，退出码: $exit_code"
        return $exit_code
    fi
}

# 监控作业状态
monitor_job() {
    log_info "监控YARN应用状态..."
    log_info "最近的应用程序:"
    yarn application -list -appStates RUNNING,SUBMITTED,ACCEPTED | grep -E "(WordCount|Application-Id)"
    
    log_info "要查看应用详细信息，请使用:"
    echo "yarn application -status <APPLICATION_ID>"
    echo "yarn logs -applicationId <APPLICATION_ID>"
}

# 显示帮助信息
show_help() {
    echo "Spark作业提交脚本"
    echo ""
    echo "用法: $0 [选项] {scala|python|monitor}"
    echo ""
    echo "命令:"
    echo "  scala    - 提交Scala版本的Spark作业"
    echo "  python   - 提交Python版本的Spark作业"
    echo "  monitor  - 监控YARN应用状态"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  --driver-memory MEM     设置Driver内存 (默认: $DRIVER_MEMORY)"
    echo "  --executor-memory MEM   设置Executor内存 (默认: $EXECUTOR_MEMORY)"
    echo "  --executor-cores NUM    设置Executor核心数 (默认: $EXECUTOR_CORES)"
    echo "  --num-executors NUM     设置Executor数量 (默认: $NUM_EXECUTORS)"
    echo "  --queue QUEUE           设置YARN队列 (默认: $QUEUE)"
    echo ""
    echo "示例:"
    echo "  $0 scala"
    echo "  $0 python"
    echo "  $0 --executor-memory 2g --num-executors 4 scala"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --driver-memory)
                DRIVER_MEMORY="$2"
                shift 2
                ;;
            --executor-memory)
                EXECUTOR_MEMORY="$2"
                shift 2
                ;;
            --executor-cores)
                EXECUTOR_CORES="$2"
                shift 2
                ;;
            --num-executors)
                NUM_EXECUTORS="$2"
                shift 2
                ;;
            --queue)
                QUEUE="$2"
                shift 2
                ;;
            scala|python|monitor)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    parse_args "$@"
    
    if [ -z "$COMMAND" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    case "$COMMAND" in
        "scala")
            check_spark
            check_yarn
            check_jar_file
            submit_scala_job
            if [ $? -eq 0 ]; then
                monitor_job
            fi
            ;;
        "python")
            check_spark
            check_yarn
            submit_python_job
            if [ $? -eq 0 ]; then
                monitor_job
            fi
            ;;
        "monitor")
            check_yarn
            monitor_job
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
