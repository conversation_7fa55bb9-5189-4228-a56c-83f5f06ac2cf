# 虚拟机分布式词频统计操作步骤

## 📋 当前状态
- ✅ JAR包已上传到 `/opt/jar/` 目录
- ✅ 数据文件需要准备（file1.txt, file2.txt, file3.txt）
- 🔄 接下来需要执行HDFS操作和Spark作业

## 🚀 完整操作步骤

### 步骤1: 检查环境和文件
```bash
# 检查当前目录和文件
pwd
ls -la /opt/jar/

# 检查Hadoop和Spark服务状态
jps

# 检查HDFS是否正常
hdfs dfs -ls /
```

### 步骤2: 准备数据文件
如果数据文件还没有上传，请先创建或上传数据文件：

```bash
# 进入工作目录
cd /opt

# 创建数据目录
mkdir -p data

# 创建测试数据文件（如果还没有的话）
cat > data/file1.txt << 'EOF'
Apache Spark is a unified analytics engine for large-scale data processing. Spark provides high-level APIs in Java, Scala, Python and R, and supports a rich set of higher-level tools including Spark SQL for SQL and structured data processing, MLlib for machine learning, GraphX for graph processing, and Structured Streaming for incremental computation and stream processing.

Spark runs on Hadoop, Apache Mesos, Kubernetes, standalone, or in the cloud. It can access diverse data sources including HDFS, Alluxio, Apache Cassandra, Apache HBase, Apache Hive, and hundreds of other data sources.

The key insight behind Spark is that many iterative algorithms and interactive data mining tools can benefit from caching datasets across iterations. Spark provides primitives for in-memory cluster computing that lets user programs load data into a cluster's memory and query it repeatedly.
EOF

cat > data/file2.txt << 'EOF'
Big data is a field that treats ways to analyze, systematically extract information from, or otherwise deal with data sets that are too large or complex to be dealt with by traditional data-processing application software. Data with many cases offer greater statistical power, while data with higher complexity may lead to a higher false discovery rate.

Big data challenges include capturing data, data storage, data analysis, search, sharing, transfer, visualization, querying, updating, information privacy and data source. Big data was originally associated with three key concepts: volume, variety, and velocity.
EOF

cat > data/file3.txt << 'EOF'
Distributed computing is a field of computer science that studies distributed systems. A distributed system is a system whose components are located on different networked computers, which communicate and coordinate their actions by passing messages to one another from any system.

The components interact with one another in order to achieve a common goal. Three significant characteristics of distributed systems are: concurrency of components, lack of a global clock, and independent failure of components.
EOF

# 验证文件创建
ls -la data/
```

### 步骤3: 上传数据文件到HDFS
```bash
# 创建HDFS输入目录
hdfs dfs -mkdir -p /user/input

# 上传数据文件到HDFS
hdfs dfs -put data/file1.txt /user/input/
hdfs dfs -put data/file2.txt /user/input/
hdfs dfs -put data/file3.txt /user/input/

# 验证文件上传成功
hdfs dfs -ls /user/input/

# 查看文件内容（可选）
hdfs dfs -cat /user/input/file1.txt | head -3
```

### 步骤4: 准备输出目录
```bash
# 删除可能存在的输出目录
hdfs dfs -rm -r /user/output 2>/dev/null

# 创建输出目录
hdfs dfs -mkdir -p /user/output

# 验证目录创建
hdfs dfs -ls /user/
```

### 步骤5: 提交Spark作业
```bash
# 进入JAR包目录
cd /opt/jar

# 提交Spark作业到YARN集群
spark-submit \
  --class WordCount \
  --master yarn \
  --deploy-mode cluster \
  --name "WordCount-DistributedTask" \
  --driver-memory 1g \
  --executor-memory 1g \
  --executor-cores 2 \
  --num-executors 2 \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.adaptive.coalescePartitions.enabled=true \
  spark-word-count-1.0.0-shaded.jar \
  /user/input \
  /user/output

# 注意：如果上传的是其他名称的JAR文件，请相应修改文件名
```

### 步骤6: 监控作业执行
```bash
# 查看YARN应用状态
yarn application -list

# 查看最近的应用（可选）
yarn application -list -appStates RUNNING,SUBMITTED,ACCEPTED

# 如果需要查看详细日志，使用应用ID
# yarn logs -applicationId <APPLICATION_ID>
```

### 步骤7: 等待作业完成并检查结果
```bash
# 等待作业完成（通常需要1-2分钟）
echo "等待作业完成..."
sleep 60

# 检查输出目录
hdfs dfs -ls /user/output/

# 查看结果文件
hdfs dfs -ls /user/output/

# 预览结果内容
echo "=== 词频统计结果预览 ==="
hdfs dfs -cat /user/output/*.csv | head -20
```

### 步骤8: 下载结果到本地
```bash
# 创建本地结果目录
mkdir -p /opt/results

# 从HDFS下载结果文件
hdfs dfs -get /user/output/* /opt/results/

# 查看本地结果文件
ls -la /opt/results/

# 查看完整结果
echo "=== 完整词频统计结果 ==="
cat /opt/results/*.csv

# 查看前20个最高频词汇
echo "=== 前20个最高频词汇 ==="
cat /opt/results/*.csv | head -21
```

## 🔧 故障排除

### 如果遇到问题，可以使用以下命令诊断：

```bash
# 检查Hadoop服务
jps | grep -E "(NameNode|DataNode|ResourceManager|NodeManager)"

# 检查HDFS健康状态
hdfs dfsadmin -report

# 检查YARN节点状态
yarn node -list

# 查看YARN应用历史
yarn application -list -appStates ALL | head -10

# 如果需要重新开始，清理HDFS目录
hdfs dfs -rm -r /user/input
hdfs dfs -rm -r /user/output
```

### 常见错误解决：

1. **如果提示找不到主类 WordCount**：
   ```bash
   # 检查JAR文件内容
   jar tf /opt/jar/spark-word-count-1.0.0-shaded.jar | grep WordCount
   ```

2. **如果内存不足**：
   ```bash
   # 减少资源配置重新提交
   spark-submit \
     --class WordCount \
     --master yarn \
     --deploy-mode cluster \
     --driver-memory 512m \
     --executor-memory 512m \
     --executor-cores 1 \
     --num-executors 1 \
     spark-word-count-1.0.0-shaded.jar \
     /user/input \
     /user/output
   ```

3. **如果HDFS权限问题**：
   ```bash
   # 检查当前用户
   whoami
   
   # 检查HDFS权限
   hdfs dfs -ls -d /user/
   ```

## ✅ 任务完成标志

当您看到以下内容时，说明任务已成功完成：
1. YARN应用状态显示为 "FINISHED" 且 "SUCCEEDED"
2. `/user/output/` 目录包含结果文件
3. 本地 `/opt/results/` 目录包含下载的CSV文件
4. 可以查看到词频统计结果，格式类似：
   ```
   word,count
   data,45
   spark,32
   system,28
   ...
   ```

## 📊 预期结果示例

成功执行后，您应该能看到类似以下的词频统计结果：
- 高频词汇：data, system, spark, processing, distributed, computing
- 结果按词频降序排列
- 输出格式为CSV，包含单词和出现次数

完成所有步骤后，您就成功完成了分布式词频统计任务！
